<script lang="ts">
	import { page } from '$app/state';

	let pillIndicatorElement: HTMLSpanElement;

	const navItems = [
		{ href: '/', label: 'Home' },
		{ href: '/o-nas', label: 'O nás' },
		{ href: '/cenik', label: '<PERSON><PERSON><PERSON> negnegr' },
		{ href: '/kontakt', label: 'Kontakt' },
		{ href: '/stan-se-lektorem', label: 'Staň se lektorem' }
	];

	function updatePillIndicator(pathname: string) {
		const activeLink = document.querySelector(`a[href="${pathname}"]`) as HTMLAnchorElement;
		if (!activeLink || !pillIndicatorElement) return;

		const linkRect = activeLink.getBoundingClientRect();
		const navRect = activeLink.closest('.header-nav')?.getBoundingClientRect();

		if (!navRect) return;

		const leftOffset = linkRect.left - navRect.left;
		const linkWidth = linkRect.width;

		pillIndicatorElement.style.left = `${leftOffset}px`;
		pillIndicatorElement.style.width = `${linkWidth}px`;
		pillIndicatorElement.style.transform = 'translateY(-50%)';
	}

	$effect(() => {
		updatePillIndicator(page.url.pathname);
	});
</script>

<nav class="header-nav">
	{#each navItems as item (item.href)}
		<a href={item.href} class:active={item.href === page.url.pathname}>
			{item.label}
		</a>
	{/each}

	<span class="pill-indicator" bind:this={pillIndicatorElement}></span>
</nav>

<style lang="scss">
	.header-nav {
		position: relative;
		width: 100%;
		background-color: var(--color-dark);
		height: var(--header-height);
		border-radius: calc(var(--header-height) / 2);
		display: flex;
		align-items: center;
		justify-content: space-between;
		isolation: isolate;
		padding: 0 var(--spacing-m);

		--pill-height: calc(var(--header-height) - var(--spacing-s) * 2);

		a {
			color: var(--color-light);
			text-decoration: none;
			padding: 0 var(--spacing-m);
			height: var(--pill-height);
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: calc(var(--pill-height) / 2);
			transition: background-color 0.2s ease;

			&.active {
				color: var(--color-dark);
			}

			&:hover:not(.active) {
				background-color: rgba(255, 255, 255, 0.05);
			}
		}

		.pill-indicator {
			position: absolute;
			top: 50%;
			left: 0;
			width: 0;
			z-index: -1;
			height: var(--pill-height);
			border-radius: calc(var(--pill-height) / 2);
			background-color: var(--color-light);
			transition:
				left 0.2s ease,
				width 0.2s ease;
			transform: translateY(-50%);
		}
	}
</style>
