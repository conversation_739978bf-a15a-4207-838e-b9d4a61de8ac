<script lang="ts">
	import type { Snippet } from 'svelte';

	import Footer from '$lib/components/Footer/Footer.svelte';
	import Header from '$lib/components/Header/Header.svelte';
	import Spacer from '$lib/components/primitives/Spacer/Spacer.svelte';

	let { children } = $props<{ children: Snippet }>();
</script>

<svelte:head>
	<title><PERSON><PERSON></title>
</svelte:head>

<div class="container">
	<Header />
	<Spacer direction="vertical" size="xxl" />

	{@render children?.()}
</div>

<Footer />

<style lang="scss">
	.container {
		max-width: 1280px;
		width: 100%;
		margin: 0 auto;
		font-size: var(--font-size-text);
		padding: 0 var(--spacing-m);

		// Tablet
		@media (min-width: 768px) {
			padding: 0 var(--spacing-xl);
		}

		// Desktop screens
		@media (min-width: 1024px) {
			padding: 0 var(--spacing-xl);
		}

		// Large desktop screens
		@media (min-width: 1344px) {
			padding: 0;
		}
	}
</style>
